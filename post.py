
posts = [
  {
    "post_caption": "Admission open for Kathmandu Metropolitan City Scholarship Program preparation! Live Classes Ongoing! Only At Rs. 999/-💸 Features: ✅Daily Live Classes ✅Model Sets ✅Weekly Mock Test ✅Discussion Forum ✅Ask With Gurus ✅4000+ MCQs Enroll Now! Download the Ambition Guru app: https://tny.ws/ambition For More Information: 📞 Contact: 01-5970295 🌐 Visit: www.ambition.guru #KathmanduMahanagarEntrancePreparation #AdmissionOpen #AmbitionGuru #EnrollNow #ElearningPlatform #ScholarshipPreparation",
    "comment": "Kismat kharab"
  },
  {
    "post_caption": "𝐅𝐔𝐋𝐋 𝐒𝐂𝐇𝐎𝐋𝐀𝐑𝐒𝐇𝐈𝐏 𝐈𝐍𝐒𝐈𝐃𝐄 𝐊𝐀𝐓𝐇𝐌𝐀𝐍𝐃𝐔 𝐌𝐄𝐓𝐑𝐎𝐏𝐎𝐋𝐈𝐓𝐀𝐍 𝐂𝐈𝐓𝐘! Kathmandu Metro is offering FULL SCHOLARSHIPS for +2 in Science, Management, Humanities, and Law. To get this scholarship, you need to pass an entrance exam conducted by Kathmandu Metro. How 𝐀𝐦𝐛𝐢𝐭𝐢𝐨𝐧 𝐆𝐮𝐫𝐮 can help: - Special entrance preparation classes - Expert guidance to help you succeed - Strategies to boost your confidence and performance 𝐋𝐢𝐦𝐢𝐭𝐞𝐝 𝐬𝐞𝐚𝐭𝐬 𝐚𝐫𝐞 𝐚𝐯𝐚𝐢𝐥𝐚𝐛𝐥𝐞 ‼️ How to Join? Download Our AmbitionGuru App: https://tny.ws/AmbitionGuru For More Details Contact Us: 01-5970295 Visit Us: Put<PERSON><PERSON>k, Kathmandu #AmbitionGuru #Freeeducation #Kathmandumetro #ktm #Scholarship #plus2 #learnfromthebest",
    "comment": "Why we are not able to call in that no??? How to contact"
  },
  {
    "post_caption": "MISSION लोकसेवा २०८२ अन्तर्गत नायब सुब्बा/खरिदार पदको बृहत् अभिमुखीकरण कक्षा हुँदैछ। कक्षामा सहभागी भई लाभ लिनुहोला। यो Zoom Id प्रयोग गरेर कक्षामा जोडिनुहोला । Zoom Id: 82998774959 हाम्रा कक्षाका विशेषताहरु: ✅ DAILY LIVE CLASSES ✅ एक पटक भर्ना भएपछि परीक्षा सम्म पढ्न पाइने ✅ बिना इन्टरनेट FREE RECORDED VIDEOS ✅ दैनिक प्रश्नोत्तर कक्षा ✅ FREE NOTES नयाँ कक्षा भर्नाको लागिः 01-5970288 App Link: https://tny.ws/ambition #ambitionguruloksewa #Kubersir #nasu #kharidar",
    "comment": "Sir vnisyona hau 🤦🏿 ignore nagaresyona"
  },
  {
    "post_caption": "MISSION लोकसेवा २०८२ अन्तर्गत नायब सुब्बा/खरिदार पदको बृहत् अभिमुखीकरण कक्षा हुँदैछ। कक्षामा सहभागी भई लाभ लिनुहोला। यो Zoom Id प्रयोग गरेर कक्षामा जोडिनुहोला । Zoom Id: 82998774959 हाम्रा कक्षाका विशेषताहरु: ✅ DAILY LIVE CLASSES ✅ एक पटक भर्ना भएपछि परीक्षा सम्म पढ्न पाइने ✅ बिना इन्टरनेट FREE RECORDED VIDEOS ✅ दैनिक प्रश्नोत्तर कक्षा ✅ FREE NOTES नयाँ कक्षा भर्नाको लागिः 01-5970288 App Link: https://tny.ws/ambition #ambitionguruloksewa #Kubersir #nasu #kharidar",
    "comment": "Ani k kin jhut jasto lagx r"
  },
  {
    "post_caption": "Image of a teacher wearing ambition guru tshirt ( caption: यो मन त मेरो …..)",
    "comment": "Yeti ko manxey le desh na khaya ko le khanxa"
  }
]


system_prompt="""
You function as an advanced sentiment analysis engine, specifically tailored for the intricacies and nuances of the Nepali language.
Provided with a diverse collection of Nepali sentences, your primary task is to conduct a granular sentiment analysis for each entry. Your analysis must delve beyond surface-level interpretation to identify subtle emotional cues, contextual implications, and instances of irony or sarcasm.
The desired output format is a JSON array, where each constituent element is a JSON object adhering to the following key-value structure:
1.  **"sentence"** — The original Nepali sentence as provided.
    
2.  **"sentiment"** — The overarching sentiment classification for the sentence. Valid categories include: "Positive", "Negative", "Neutral", "Mixed" (for sentences containing both positive and negative elements), or "Undeterminable" (for sentences that are grammatically or semantically nonsensical, lack sufficient context for analysis, or defy logical interpretation for sentiment).
    
3.  **"intensity"** — The perceived strength or degree of the identified sentiment. Valid categories include: "Strong", "Moderate", "Mild", or "N/A" (for "Neutral" or "Undeterminable" sentiments where intensity is not applicable).
    
4.  **"explanation"** — A concise, English-language rationale elucidating the basis for the assigned sentiment. This explanation should refer to specific words, phrases, grammatical structures, or contextual cues (if implied) that led to the sentiment determination. For "Undeterminable" sentences, state clearly why sentiment cannot be assessed (e.g., "The sentence is semantically nonsensical and defies logical sentiment analysis.").
    
5.  **"emotions"** — A comprehensive list of discernable emotions (e.g., joy, sorrow, anger, apprehension, surprise, disgust, contempt, anticipation, relief), if any are overtly or subtly expressed within the sentence. If no specific emotion is prominent, this should be an empty list [].
    
**Example Output Structure:**
Generated json
```
      [
  {
    "sentence": "म आज धेरै खुसी छु।",
    "sentiment": "Positive",
    "intensity": "Strong",
    "explanation": "The sentence clearly expresses happiness and joy using 'धेरै खुसी' (very happy).",
    "emotions": ["joy", "happiness"]
  },
  {
    "sentence": "परिस्थिति अप्ठ्यारो भए पनि हामीले जित्यौं।",
    "sentiment": "Mixed",
    "intensity": "Moderate",
    "explanation": "The sentence contains a negative aspect ('परिस्थिति अप्ठ्यारो' - difficult situation) combined with a strong positive outcome ('हामीले जित्यौं' - we won).",
    "emotions": ["relief", "triumph", "stress"]
  }
]
    
```
Now, analyze the following  sentence, including those with complex, ambiguous, or intentionally nonsensical structures. For the nonsensical sentences, classify their sentiment as "Undeterminable".

{}
"""