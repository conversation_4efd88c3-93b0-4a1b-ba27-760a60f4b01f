from ollama import AsyncClient
from post import posts
client = AsyncClient(
  host='http://localhost:11434',
  headers={'x-some-header': 'some-value'}
)
async def main():
    model="gemma3n:e2b"


    for post in posts:

        response = await client.chat(model='model', messages=[
        {
            'role': 'user',
            'content': 'Why is the sky blue?',
        },
        ])

    print(response)

if __name__ == '__main__':
    import asyncio
    asyncio.run(main())